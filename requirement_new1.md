# 团购详情页垂直行业功能优化需求规范

## 1. 需求概述

### 1.1 项目背景
基于商家反馈和实际业务需求，对团购详情页进行垂直行业功能优化，主要涉及眼镜、眼科、口腔等专业领域的信息展示和用户体验提升。

### 1.2 项目目标
- 提升垂直行业团购详情页的专业性和信息完整性
- 优化用户体验，提供更精准的服务信息展示
- 增强商家后台管理功能，提供翻单引导和数据清洗能力
- 建立标准化的行业信息展示规范

### 1.3 适用范围
- 眼镜行业：近视配镜、儿童配镜、老花眼镜、仅镜框、仅镜片、太阳眼镜、隐形眼镜
- 眼科行业：儿童普通眼镜、医学配镜、离焦镜、OK镜、离焦软镜、RGP镜、其他接触镜
- 口腔行业：补牙、儿童补牙等相关服务

---

## 2. 功能模块规范

### 2.1 商家后台管理功能

#### 2.1.1 翻单引导标签系统

##### 功能目标
为商家提供团单完善度提示，引导商家完善团单信息以获得更好的曝光效果。

##### 2.1.1.1 "待升级"标签功能

**触发条件**：
- 团单类目存在必填属性未填写的情况
- 适用于眼镜、眼科、口腔齿科等二级类目

**展示规则**：
- 标签位置：开店宝后台团单列表页
- 标签样式：显著的"待升级"标识
- 交互方式：鼠标悬停显示说明浮层

**浮层文案规范**：
```
为了您的团单获得更多精准曝光，请尽快更新团单内容，更新后团单销量不受影响，具体操作：
1. 点击右侧'编辑'
2. 完善团单内容并提交
```

**验收标准**：
- 必填属性检测准确率100%
- 浮层显示响应时间<200ms
- 标签状态实时更新

##### 2.1.1.2 "待更新"标签功能

**触发条件**：
- 团单使用旧版类目结构
- 涉及眼科、眼镜、口腔齿科的二级和三级类目

**展示规则**：
- 标签显示："待更新"标识
- 编辑触发：点击编辑时弹出类目选择引导弹窗
- 处理方式：通过B端数据清洗实现类目迁移

**交互流程**：
1. 商家点击编辑旧类目团单
2. 系统弹出新类目选择引导弹窗
3. 商家选择对应的新类目
4. 系统完成数据迁移和映射

---

### 2.2 眼镜行业功能模块

#### 2.2.1 质保信息展示优化

##### 功能目标
支持多种时间单位的质保信息展示，提升信息表达的灵活性和准确性。

##### 实现规范

**支持单位**：
- 年：适用于长期质保
- 天：适用于短期质保或特殊服务

**展示格式**：
- 分项质保：`镜片[X][单位]质保，镜框[Y][单位]质保`
- 统一质保：`[Z][单位]质保`

**数据兼容性**：
- 向下兼容现有"年"单位数据
- 新增数据支持"天"单位输入
- 自动识别和转换显示格式

**验收标准**：
- 支持年、天两种单位正确显示
- 现有数据展示不受影响
- 新数据录入和展示功能正常

#### 2.2.2 取镜时间类型扩展

##### 功能目标
提供多样化的取镜时间表达方式，满足不同配镜服务的时间需求。

##### 支持类型

**立等可取**：
- 显示：`立等可取`
- 适用：现场配镜服务

**指定天数后可取**：
- 显示：`[X]天后可取`
- 适用：需要加工时间的配镜服务

**指定天数内可取**：
- 显示：`[X]天内可取`
- 适用：灵活取镜时间的服务

**指定天数范围可取**：
- 显示：`[X] - [Y]天可取`
- 适用：需要一定加工周期的复杂配镜

##### 数据字段设计
- `acquire_time_type`：取镜时间类型枚举
- `specify_days`：指定天数
- `specify_days_max`：范围最大天数

#### 2.2.3 标题拼接逻辑优化

##### 功能目标
优化团单标题生成逻辑，提供更灵活的标题管理方式。

##### 实现规范

**拼接模式调整**：
- 从模式3（固定前拼接）调整为模式2（推荐前拼接）
- 分隔符统一：使用全角"｜"替换半角"|"

**用户交互流程**：
1. 商家选择属性后，系统自动生成推荐标题
2. 标题自动前拼接相关属性信息
3. 商家可选择"不使用推荐名称"进入自定义模式
4. 自定义模式下商家可完全自由编辑标题

**验收标准**：
- 推荐标题生成准确率>95%
- 自定义切换功能正常
- 分隔符显示统一

#### 2.2.4 度数/折射率科普功能

##### 功能目标
为配镜用户提供专业的度数和折射率关系科普信息，提升用户对产品的理解。

##### 实现规范

**适用类目**：
- 近视配镜、儿童配镜、仅镜片、老花眼镜

**入口位置**：
- 镜片标题行最右侧
- 或团单类目标题行最右侧

**交互设计**：
- 入口样式：问号图标或"说明"文字链接
- 点击触发：弹出科普信息浮层
- 浮层内容：度数与折射率的关系说明

**内容要求**：
- 专业准确的科普信息
- 通俗易懂的表达方式
- 图文并茂的展示形式

#### 2.2.5 镜片技术多选功能

##### 功能目标
支持多种镜片技术的同时展示，提供更完整的产品信息。

##### 实现规范

**字段变更**：
- 从单选改为多选支持
- 前端显示：多个技术用"、"分隔

**用户体验优化**：
- 新增技术提报入口
- 提报文案：`如添加时找不到对应的镜片技术，可点击提报`
- 提报流程：引导用户提交新技术建议

**数据处理**：
- 支持数组类型存储
- 兼容现有单选数据
- 提供数据迁移方案

#### 2.2.6 验光操作人员多选功能

##### 功能目标
支持多个验光操作人员的展示，适应多门店和多人员的业务场景。

##### 实现规范

**显示逻辑**：
- 单选：`[验光师姓名]（从业[经验年限]）`
- 多选：`[验光师1]/[验光师2]/[验光师3]可选`

**多门店处理**：
- 判断条件：可用门店数量 > 1
- 显示方式：添加气泡提示
- 提示内容：`该团购多门店可用，实际验光操作人员可联系商家确认`

**数据字段**：
- `optometrist2`：验光师列表（数组类型）
- `optometrist_experience`：从业经验（仅单选时显示）

---

### 2.3 眼科行业功能模块

#### 2.3.1 验光操作人员统一化

##### 功能目标
统一眼科行业的验光操作人员展示逻辑，与眼镜行业保持一致。

##### 实现规范

**字段统一**：
- 使用 `OperatorsSelection` 统一字段
- 移除按职业类型分别处理的逻辑（医师/验光师/护士）

**适用类目**：
- 儿童医学验光
- 成人医学验光  
- 角膜接触镜配镜检查
- 视觉训练

**显示规则**：
- 与眼镜行业验光操作人员显示逻辑完全一致
- 支持多选和多门店提示功能

#### 2.3.2 健康检查模块优化

##### 功能目标
优化健康整体评估模块的信息展示逻辑。

##### 实现规范

**商家说明处理**：
- 当商家说明未填写时，C端不显示默认文案
- 避免显示无意义的占位文本
- 提升用户体验的专业性

---

### 2.4 口腔行业功能模块

#### 2.4.1 3M材料科普信息功能

##### 功能目标
为3M树脂补牙材料提供专业的科普信息展示，帮助用户了解不同材料的特性。

##### 触发条件
- `Technique` = `树脂补牙`
- `MaterialBrand` = `3M`

##### 支持材料型号
- Z250：展示对应特性图片和说明
- Z350：展示对应特性图片和说明  
- P60：展示对应特性图片和说明
- P90：展示对应特性图片和说明

##### 实现规范

**展示位置**：
- 团详页重点展示信息模块
- 材料型号字段附近

**内容管理**：
- 图片资源通过配置化管理
- 支持动态更新和维护
- 确保内容的专业性和准确性

**交互设计**：
- 点击触发科普信息弹窗
- 弹窗标题：`3M树脂材料系列对比`
- 根据具体型号显示对应图片

#### 2.4.2 服务时长范围功能

##### 功能目标
支持服务时长的范围表达，提供更准确的时间预期。

##### 实现规范

**显示格式**：
- 单一时长：`[X]分钟`
- 范围时长：`[X] - [Y]分钟`
- 自定义单位：支持通过 `ServiceDurationUnits2` 字段设置

**总时长计算**：
- 计算条件：所有服务流程使用相同时间单位
- 计算逻辑：范围值取平均值参与计算
- 显示格式：`共[总时长][单位]`
- 异常处理：单位不一致时不显示总时长

**数据字段设计**：
- `duration`：基础时长
- `durationRangeMax`：范围最大值
- `ServiceDurationUnits2`：时间单位

#### 2.4.3 套餐包含信息功能

##### 功能目标
为口腔套餐服务提供包含内容的详细展示。

##### 实现规范

**适用范围**：
- 仅限指定的口腔类目
- 套餐类型的团购服务

**展示方式**：
- 支持多选枚举展示
- 示例内容：`含洗牙1次`、`含涂氟1次`

**字段设计**：
- `package_includes`：套餐包含内容数组
- 支持动态配置可选项

#### 2.4.4 服务时长数据清洗

##### 功能目标
对不需要展示服务时长的场景进行数据清洗，避免无效信息展示。

##### 实现规范

**清洗规则**：
- 识别不需要展示服务时长的服务流程
- 对历史数据进行批量清洗
- 确保团详页不显示无效时长信息

**执行方式**：
- 后台批量数据处理
- 实时数据验证和过滤
- 定期数据质量检查

---

## 3. 技术实现要求

### 3.1 数据结构设计

#### 3.1.1 新增字段
- `titleFontWeight`：标题字体粗细控制
- `durationRangeMax`：服务时长范围最大值  
- `ServiceDurationUnits2`：服务时长单位
- `optometrist2`：验光师列表（数组类型）
- `OperatorsSelection`：操作人员选择（数组类型）

#### 3.1.2 字段变更
- 镜片技术字段：从单选改为多选
- 验光操作人员：从单选改为多选
- 质保信息：增加单位属性支持

### 3.2 配置化管理

#### 3.2.1 新增配置项
- `GLASSES_MATERIAL_DETAIL`：眼镜材料详情配置
- `MATERIAL_COMPARISON_PIC`：材料对比图片URL配置
- 翻单引导标签配置
- 科普信息内容配置

### 3.3 兼容性要求

#### 3.3.1 数据兼容
- 新字段提供默认值处理
- 保持对现有数据格式的完全支持
- 提供平滑的数据迁移方案

#### 3.3.2 功能兼容  
- 新功能不影响现有展示逻辑
- 提供功能开关控制
- 支持灰度发布和回滚

---

## 4. 验收标准

### 4.1 功能验收标准

#### 4.1.1 眼镜行业功能
- [ ] 质保信息支持年、天单位正确显示
- [ ] 取镜时间四种类型正确展示
- [ ] 标题拼接逻辑按新规则执行
- [ ] 度数/折射率科普入口正常显示和交互
- [ ] 镜片技术多选功能正常
- [ ] 验光操作人员多选和多门店提示正确

#### 4.1.2 眼科行业功能
- [ ] 验光操作人员统一字段正确处理
- [ ] 健康检查模块商家说明逻辑正确

#### 4.1.3 口腔行业功能
- [ ] 3M材料科普信息条件触发正确
- [ ] 服务时长范围计算和显示准确
- [ ] 套餐包含信息正确展示
- [ ] 服务时长数据清洗完成

#### 4.1.4 商家后台功能
- [ ] 待升级标签检测和显示正确
- [ ] 待更新标签和引导弹窗正常
- [ ] 浮层文案和交互符合规范

### 4.2 性能验收标准
- [ ] 页面加载时间无明显增加（<5%）
- [ ] 配置数据获取响应时间<500ms
- [ ] 图片资源加载优化，首屏时间<2s

### 4.3 兼容性验收标准
- [ ] 现有数据展示100%正常
- [ ] 新旧字段切换无异常
- [ ] 降级场景功能完全正常
- [ ] 多浏览器兼容性测试通过

### 4.4 用户体验验收标准
- [ ] 交互流程符合用户习惯
- [ ] 信息展示层次清晰
- [ ] 科普内容专业准确
- [ ] 错误提示友好明确
